"""度量收集模块"""
from typing import Dict, Any, Optional
import time

try:
    from opentelemetry import metrics
    from opentelemetry.sdk.metrics import MeterProvider
    from opentelemetry.sdk.metrics.export import ConsoleMetricExporter, PeriodicExportingMetricReader
    from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
    OPENTELEMETRY_AVAILABLE = True
except ImportError:
    OPENTELEMETRY_AVAILABLE = False
    metrics = None


class MockMeter:
    """模拟度量器，当OpenTelemetry不可用时使用"""
    
    def create_counter(self, name: str, **kwargs):
        return MockCounter(name)
    
    def create_histogram(self, name: str, **kwargs):
        return MockHistogram(name)
    
    def create_gauge(self, name: str, **kwargs):
        return MockGauge(name)


class MockCounter:
    """模拟计数器"""
    
    def __init__(self, name: str):
        self.name = name
        self.value = 0
    
    def add(self, amount: float, attributes: Optional[Dict[str, Any]] = None) -> None:
        self.value += amount


class MockHistogram:
    """模拟直方图"""
    
    def __init__(self, name: str):
        self.name = name
        self.values = []
    
    def record(self, amount: float, attributes: Optional[Dict[str, Any]] = None) -> None:
        self.values.append(amount)


class MockGauge:
    """模拟仪表"""
    
    def __init__(self, name: str):
        self.name = name
        self.value = 0
    
    def set(self, amount: float, attributes: Optional[Dict[str, Any]] = None) -> None:
        self.value = amount


class PhoenixMetrics:
    """PhoenixFrame度量收集器"""
    
    def __init__(self, name: str):
        self.name = name
        if OPENTELEMETRY_AVAILABLE:
            self.meter = metrics.get_meter(name)
        else:
            self.meter = MockMeter()
        
        # 创建常用度量
        self._test_counter = self.meter.create_counter(
            "test_cases_total",
            description="Total number of test cases executed"
        )
        
        self._test_duration = self.meter.create_histogram(
            "test_duration_seconds",
            description="Test case execution duration in seconds"
        )
        
        self._api_request_counter = self.meter.create_counter(
            "api_requests_total",
            description="Total number of API requests"
        )
        
        self._api_response_time = self.meter.create_histogram(
            "api_response_time_seconds",
            description="API response time in seconds"
        )
        
        self._test_status_counter = self.meter.create_counter(
            "test_status_total",
            description="Test cases by status"
        )
    
    def record_test_execution(self, test_name: str, duration: float, status: str) -> None:
        """记录测试执行度量"""
        attributes = {"test_name": test_name, "status": status}
        
        self._test_counter.add(1, attributes)
        self._test_duration.record(duration, attributes)
        self._test_status_counter.add(1, {"status": status})
    
    def record_api_request(self, method: str, url: str, status_code: int, 
                          response_time: float) -> None:
        """记录API请求度量"""
        attributes = {
            "method": method,
            "url": url,
            "status_code": str(status_code)
        }
        
        self._api_request_counter.add(1, attributes)
        self._api_response_time.record(response_time, attributes)
    
    def record_page_action(self, action: str, element: str = "", duration: float = 0) -> None:
        """记录页面操作度量"""
        page_action_counter = self.meter.create_counter(
            "page_actions_total",
            description="Total number of page actions"
        )
        
        attributes = {"action": action}
        if element:
            attributes["element"] = element
        
        page_action_counter.add(1, attributes)
        
        if duration > 0:
            page_action_duration = self.meter.create_histogram(
                "page_action_duration_seconds",
                description="Page action duration in seconds"
            )
            page_action_duration.record(duration, attributes)


class TestTimer:
    """测试计时器上下文管理器"""
    
    def __init__(self, metrics: PhoenixMetrics, test_name: str):
        self.metrics = metrics
        self.test_name = test_name
        self.start_time = None
        self.status = "unknown"
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            if exc_type is None:
                self.status = "passed"
            else:
                self.status = "failed"
            
            self.metrics.record_test_execution(self.test_name, duration, self.status)
    
    def set_status(self, status: str) -> None:
        """手动设置测试状态"""
        self.status = status


# 全局度量器实例
_meters: Dict[str, PhoenixMetrics] = {}
_meter_provider: Optional[Any] = None


def setup_metrics(service_name: str = "phoenixframe",
                 otlp_endpoint: Optional[str] = None,
                 console_export: bool = False,
                 export_interval: int = 60) -> None:
    """
    设置度量收集
    
    Args:
        service_name: 服务名称
        otlp_endpoint: OTLP导出端点
        console_export: 是否启用控制台导出
        export_interval: 导出间隔（秒）
    """
    global _meter_provider
    
    if not OPENTELEMETRY_AVAILABLE:
        print("Warning: OpenTelemetry is not available. Metrics will be disabled.")
        return
    
    readers = []
    
    # 添加导出器
    if otlp_endpoint:
        otlp_exporter = OTLPMetricExporter(endpoint=otlp_endpoint)
        readers.append(PeriodicExportingMetricReader(otlp_exporter, export_interval_millis=export_interval * 1000))
    
    if console_export:
        console_exporter = ConsoleMetricExporter()
        readers.append(PeriodicExportingMetricReader(console_exporter, export_interval_millis=export_interval * 1000))
    
    # 创建MeterProvider
    _meter_provider = MeterProvider(metric_readers=readers)
    metrics.set_meter_provider(_meter_provider)


def get_meter(name: str) -> PhoenixMetrics:
    """
    获取度量器实例
    
    Args:
        name: 度量器名称
        
    Returns:
        PhoenixMetrics: 度量器实例
    """
    if name not in _meters:
        _meters[name] = PhoenixMetrics(name)
    return _meters[name]
