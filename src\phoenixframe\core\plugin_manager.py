import importlib

class PluginManager:
    def __init__(self):
        self._loaded_plugins = {}

    def load_plugin(self, plugin_name):
        """Loads a plugin by its module name."""
        if plugin_name in self._loaded_plugins:
            return
        try:
            plugin_module = importlib.import_module(plugin_name)
            # A simple convention: the plugin class is the camel-cased version of the module name
            class_name = "".join(word.capitalize() for word in plugin_name.split("_")) + "Plugin"
            plugin_class = getattr(plugin_module, class_name)
            self._loaded_plugins[plugin_name] = plugin_class()
        except (ImportError, AttributeError) as e:
            raise ImportError(f"Could not load plugin '{plugin_name}': {e}")

    def is_plugin_loaded(self, plugin_name):
        """Checks if a plugin is loaded."""
        return plugin_name in self._loaded_plugins
