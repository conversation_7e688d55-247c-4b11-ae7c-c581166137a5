"""API客户端，支持自动认证和链式断言"""
import requests
from typing import Any, Dict, Optional, Union
from urllib.parse import urljoin
import uuid
from ..core.config import get_config
from .response import APIResponse


class APIClient:
    """API客户端，提供统一的HTTP请求接口"""
    
    def __init__(self, base_url: Optional[str] = None, auth_strategy=None):
        """
        初始化API客户端
        
        Args:
            base_url: 基础URL，如果不提供则从配置中获取
            auth_strategy: 认证策略，用于自动处理认证
        """
        self.config = get_config()
        self.base_url = base_url or self.config.environment.base_url
        self.auth_strategy = auth_strategy
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': f'PhoenixFrame/{self.config.version}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def _prepare_url(self, endpoint: str) -> str:
        """准备完整URL"""
        if endpoint.startswith(('http://', 'https://')):
            return endpoint
        return urljoin(self.base_url, endpoint.lstrip('/'))
    
    def _prepare_headers(self, headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """准备请求头，包括认证和追踪信息"""
        request_headers = self.session.headers.copy()
        
        if headers:
            request_headers.update(headers)
        
        # 添加追踪ID
        trace_id = str(uuid.uuid4())
        request_headers['X-Trace-ID'] = trace_id
        
        # 如果有认证策略，应用认证
        if self.auth_strategy:
            auth_headers = self.auth_strategy.get_auth_headers()
            request_headers.update(auth_headers)
        
        return request_headers
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """发送HTTP请求"""
        url = self._prepare_url(endpoint)
        headers = self._prepare_headers(kwargs.pop('headers', None))
        
        # 发送请求
        response = self.session.request(
            method=method,
            url=url,
            headers=headers,
            **kwargs
        )
        
        return APIResponse(response)
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, 
            headers: Optional[Dict[str, str]] = None, **kwargs) -> APIResponse:
        """发送GET请求"""
        return self._make_request('GET', endpoint, params=params, headers=headers, **kwargs)
    
    def post(self, endpoint: str, json: Optional[Dict[str, Any]] = None,
             data: Optional[Union[str, Dict[str, Any]]] = None,
             files: Optional[Dict[str, Any]] = None,
             headers: Optional[Dict[str, str]] = None, **kwargs) -> APIResponse:
        """发送POST请求"""
        return self._make_request('POST', endpoint, json=json, data=data, 
                                files=files, headers=headers, **kwargs)
    
    def put(self, endpoint: str, json: Optional[Dict[str, Any]] = None,
            data: Optional[Union[str, Dict[str, Any]]] = None,
            headers: Optional[Dict[str, str]] = None, **kwargs) -> APIResponse:
        """发送PUT请求"""
        return self._make_request('PUT', endpoint, json=json, data=data, 
                                headers=headers, **kwargs)
    
    def patch(self, endpoint: str, json: Optional[Dict[str, Any]] = None,
              data: Optional[Union[str, Dict[str, Any]]] = None,
              headers: Optional[Dict[str, str]] = None, **kwargs) -> APIResponse:
        """发送PATCH请求"""
        return self._make_request('PATCH', endpoint, json=json, data=data, 
                                headers=headers, **kwargs)
    
    def delete(self, endpoint: str, headers: Optional[Dict[str, str]] = None, 
               **kwargs) -> APIResponse:
        """发送DELETE请求"""
        return self._make_request('DELETE', endpoint, headers=headers, **kwargs)
    
    def set_auth_strategy(self, auth_strategy):
        """设置认证策略"""
        self.auth_strategy = auth_strategy
    
    def close(self):
        """关闭会话"""
        self.session.close()
