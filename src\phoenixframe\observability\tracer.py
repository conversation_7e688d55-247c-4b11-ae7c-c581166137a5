"""分布式追踪模块"""
from typing import Optional, Dict, Any
from contextlib import contextmanager

try:
    from opentelemetry import trace
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
    from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
    from opentelemetry.sdk.resources import Resource
    OPENTELEMETRY_AVAILABLE = True
except ImportError:
    OPENTELEMETRY_AVAILABLE = False
    trace = None


class MockTracer:
    """模拟追踪器，当OpenTelemetry不可用时使用"""
    
    def start_span(self, name: str, **kwargs):
        return MockSpan(name)
    
    @contextmanager
    def start_as_current_span(self, name: str, **kwargs):
        yield MockSpan(name)


class MockSpan:
    """模拟Span"""
    
    def __init__(self, name: str):
        self.name = name
    
    def set_attribute(self, key: str, value: Any) -> None:
        pass
    
    def set_status(self, status: Any) -> None:
        pass
    
    def end(self) -> None:
        pass
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass


class PhoenixTracer:
    """PhoenixFrame追踪器"""
    
    def __init__(self, name: str):
        self.name = name
        if OPENTELEMETRY_AVAILABLE:
            self.tracer = trace.get_tracer(name)
        else:
            self.tracer = MockTracer()
    
    def start_span(self, name: str, **kwargs):
        """开始一个新的span"""
        return self.tracer.start_span(name, **kwargs)
    
    @contextmanager
    def start_as_current_span(self, name: str, **kwargs):
        """作为当前span开始追踪"""
        with self.tracer.start_as_current_span(name, **kwargs) as span:
            yield span
    
    def trace_test_case(self, test_name: str):
        """追踪测试用例"""
        return self.start_as_current_span(f"test_case:{test_name}")
    
    def trace_api_request(self, method: str, url: str):
        """追踪API请求"""
        return self.start_as_current_span(f"api_request:{method} {url}")
    
    def trace_page_action(self, action: str, element: str = ""):
        """追踪页面操作"""
        span_name = f"page_action:{action}"
        if element:
            span_name += f":{element}"
        return self.start_as_current_span(span_name)


# 全局追踪器实例
_tracers: Dict[str, PhoenixTracer] = {}
_tracer_provider: Optional[Any] = None


def setup_tracing(service_name: str = "phoenixframe", 
                 otlp_endpoint: Optional[str] = None,
                 console_export: bool = False) -> None:
    """
    设置分布式追踪
    
    Args:
        service_name: 服务名称
        otlp_endpoint: OTLP导出端点
        console_export: 是否启用控制台导出
    """
    global _tracer_provider
    
    if not OPENTELEMETRY_AVAILABLE:
        print("Warning: OpenTelemetry is not available. Tracing will be disabled.")
        return
    
    # 创建资源
    resource = Resource.create({
        "service.name": service_name,
        "service.version": "1.0.0",
    })
    
    # 创建TracerProvider
    _tracer_provider = TracerProvider(resource=resource)
    trace.set_tracer_provider(_tracer_provider)
    
    # 添加导出器
    if otlp_endpoint:
        otlp_exporter = OTLPSpanExporter(endpoint=otlp_endpoint)
        _tracer_provider.add_span_processor(BatchSpanProcessor(otlp_exporter))
    
    if console_export:
        console_exporter = ConsoleSpanExporter()
        _tracer_provider.add_span_processor(BatchSpanProcessor(console_exporter))


def get_tracer(name: str) -> PhoenixTracer:
    """
    获取追踪器实例
    
    Args:
        name: 追踪器名称
        
    Returns:
        PhoenixTracer: 追踪器实例
    """
    if name not in _tracers:
        _tracers[name] = PhoenixTracer(name)
    return _tracers[name]


def trace_decorator(operation_name: str):
    """追踪装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            tracer = get_tracer(func.__module__)
            with tracer.start_as_current_span(f"{operation_name}:{func.__name__}"):
                return func(*args, **kwargs)
        return wrapper
    return decorator
