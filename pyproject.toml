[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "phoenixframe"
version = "3.2.0"
description = "An enterprise-grade test automation framework with POM+Workflow architecture"
readme = "README.md"
authors = [{ name = "PhoenixFrame Team", email = "<EMAIL>" }]
license = { text = "MIT" }
requires-python = ">=3.9"
keywords = ["testing", "automation", "selenium", "playwright", "api", "framework"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Testing",
    "Topic :: Software Development :: Quality Assurance",
]

dependencies = [
    "pytest>=7.4.0",
    "click>=8.1.0",
    "pydantic>=2.4.0",
    "pyyaml>=6.0",
    "requests>=2.31.0",
    "selenium>=4.0.0",
    "allure-pytest>=2.13.0",
]

[project.optional-dependencies]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.0",
    "pytest-mock>=3.11.0",
    "pytest-html>=4.0.0",
    "coverage>=7.3.0",
]
dev = [
    "ruff>=0.1.0",
    "black>=23.0.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
    "bandit>=1.7.0",
    "safety>=2.3.0",
]
web = [
    "playwright>=1.40.0",
    "selenium>=4.0.0",
]
observability = [
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    "opentelemetry-exporter-otlp>=1.21.0",
]
security = [
    "cryptography>=41.0.0",
    "hvac>=1.2.0",  # HashiCorp Vault client
    "boto3>=1.34.0",  # AWS SDK
]
ocr = [
    "pytesseract>=0.3.10",
    "pillow>=10.0.0",
    "opencv-python>=4.8.0",
]
all = [
    "phoenixframe[test,dev,web,observability,security,ocr]"
]

[project.urls]
Homepage = "https://github.com/phoenixframe/phoenixframe"
Documentation = "https://phoenixframe.readthedocs.io"
Repository = "https://github.com/phoenixframe/phoenixframe"
"Bug Tracker" = "https://github.com/phoenixframe/phoenixframe/issues"

[project.scripts]
phoenix = "phoenixframe.cli:main"

# Tool configurations
[tool.ruff]
target-version = "py39"
line-length = 100
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011"]

[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--disable-warnings",
    "-ra",
]
testpaths = ["tests"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow running tests",
    "web: Web automation tests",
    "api: API tests",
    "selenium: Selenium web tests",
    "playwright: Playwright web tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

[tool.coverage.run]
source = ["src"]
branch = true
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101", "B601"]
