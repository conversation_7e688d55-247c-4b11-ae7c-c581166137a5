[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "phoenixframe"
version = "3.2.0"
description = "An enterprise-grade test automation framework."
authors = [{ name = "Your Name", email = "<EMAIL>" }]
license = "MIT"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

dependencies = [
    "pytest>=7.4.0",
    "click>=8.1.0",
    "pydantic>=2.4.0",
    "pyyaml>=6.0",
    "ruff>=0.1.0",
    "pytest-cov>=4.1.0",
    "selenium>=4.0.0",
]

[tool.setuptools]
packages = ["phoenixframe"]
