import os
from pathlib import Path
import click
import yaml
from . import doctor
from . import env as env_module

@click.group()
def main():
    """PhoenixFrame CLI"""
    pass


@main.command()
@click.argument("project_name", type=click.Path())
def init(project_name):
    """Initialize a new PhoenixFrame project."""
    project_path = Path(project_name).resolve()
    if project_path.exists() and any(project_path.iterdir()):
        click.echo(f"Error: Directory '{project_path}' already exists and is not empty.", err=True)
        return

    click.echo(f"Initializing project '{project_name}' at '{project_path}'...")

    try:
        # Create directories
        expected_dirs = [
            "src/phoenixframe",
            "tests",
            "configs",
            "data",
            "docs",
            "templates",
        ]
        for d in expected_dirs:
            dir_path = project_path / d
            dir_path.mkdir(parents=True, exist_ok=True)
            click.echo(f"  Created directory: {dir_path}")

        # Create files
        project_config = {
            "app_name": project_path.name,
            "version": "1.0",
            "environments": {
                "default": {
                    "base_url": "http://localhost:8000",
                    "description": "Default environment"
                }
            },
            "reporting": {
                "allure": {
                    "enabled": True,
                    "report_dir": "allure-report",
                    "results_dir": "allure-results"
                }
            }
        }
        
        files_to_create = {
            "configs/phoenix.yaml": yaml.dump(project_config, default_flow_style=False, allow_unicode=True),
            "pyproject.toml": f'[project]\nname = "{project_name}"\nversion = "0.1.0"\n',
            "README.md": f"# {project_name}\n\nThis project was initialized by PhoenixFrame.",
            ".gitignore": "# PhoenixFrame\n.venv/\n__pycache__/\n*.pyc\n\n# Reports\nallure-report/\nallure-results/\n",
        }

        for file_path, content in files_to_create.items():
            path = project_path / file_path
            path.write_text(content, encoding="utf-8")
            click.echo(f"  Created file: {path}")

        click.echo(f"\nProject '{project_name}' initialized successfully.")

    except Exception as e:
        click.echo(f"Error during project initialization: {e}", err=True)


@main.command()
def report():
    """Generate and serve the Allure report."""
    import subprocess
    try:
        subprocess.run(["allure", "serve", "allure-results"], check=True)
    except FileNotFoundError:
        click.echo("Error: 'allure' command not found. Please make sure Allure is installed and in your PATH.", err=True)
    except subprocess.CalledProcessError as e:
        click.echo(f"Error serving Allure report: {e}", err=True)

@main.command(context_settings={'ignore_unknown_options': True, 'allow_extra_args': True})
@click.argument("test_paths", nargs=-1, type=click.Path(exists=True))
@click.option("--env", "-e", help="Specify the environment to use for testing")
@click.pass_context
def run(ctx, test_paths, env):
    """Run tests with optional environment and pytest arguments.

    Extra pytest arguments can be passed after a '--' separator.
    Example: phoenix run tests/ -- -v -k "login_test"
    """
    from .core.runner import PhoenixRunner
    from . import env as env_module

    # Set environment if specified
    if env:
        # TODO: Implement environment switching logic
        click.echo(f"Using environment: {env}")

    # Get extra arguments from context
    pytest_extra_args = ctx.args if ctx.args else []

    runner = PhoenixRunner()
    result = runner.run_tests(test_paths=list(test_paths), pytest_extra_args=pytest_extra_args)
    exit(result.get("pytest_exit_code", 0))



@main.command()
def doctor():
    """Check environment configuration and dependencies."""
    from . import doctor as doctor_module
    doctor_module.run_checks()

@main.group()
def env():
    """Manage test environments."""
    pass

@env.command("list")
def list_envs():
    """List all configured environments."""
    env_module.list_environments()

@main.group()
def generate():
    """Generate test code from various assets."""
    pass

@generate.command()
@click.argument("har_file", type=click.Path(exists=True, dir_okay=False, readable=True))
@click.option("--output", "-o", type=click.Path(dir_okay=False, writable=True), default="generated_api_test.py", help="Output file path for the generated test code.")
def har(har_file, output):
    """Generate API test code from a HAR file.

    HAR_FILE: Path to the HAR file.
    """
    from .codegen.har_parser import HARParser
    from .codegen.api_generator import APITestGenerator

    click.echo(f"Generating API test code from {har_file}...")
    try:
        with open(har_file, "r", encoding="utf-8") as f:
            har_content = f.read()

        parser = HARParser()
        parsed_data = parser.parse(har_content)

        generator = APITestGenerator()
        generated_code = generator.generate(parsed_data)

        output_path = Path(output)
        if not output_path.parent.exists():
            output_path.parent.mkdir(parents=True)

        with open(output_path, "w", encoding="utf-8") as f:
            f.write(generated_code)

        click.echo(f"Successfully generated test code to {output_path}")
    except Exception as e:
        click.echo(f"Error generating code: {e}", err=True)
        exit(1)

@generate.command()
@click.argument("openapi_file", type=click.Path(exists=True, dir_okay=False, readable=True))
@click.option("--output", "-o", type=click.Path(dir_okay=False, writable=True), default="generated_openapi_test.py", help="Output file path for the generated test code.")
def openapi(openapi_file, output):
    """Generate API test code skeletons from an OpenAPI/Swagger file.

    OPENAPI_FILE: Path to the OpenAPI/Swagger file (YAML or JSON).
    """
    from .codegen.openapi_parser import OpenAPIParser
    from .codegen.openapi_generator import OpenAPITestGenerator

    click.echo(f"Generating OpenAPI test code skeletons from {openapi_file}...")
    try:
        with open(openapi_file, "r", encoding="utf-8") as f:
            openapi_content = f.read()

        parser = OpenAPIParser()
        parsed_data = parser.parse(openapi_content)

        generator = OpenAPITestGenerator()
        generated_code = generator.generate(parsed_data)

        output_path = Path(output)
        if not output_path.parent.exists():
            output_path.parent.mkdir(parents=True)

        with open(output_path, "w", encoding="utf-8") as f:
            f.write(generated_code)

        click.echo(f"Successfully generated test code to {output_path}")
    except Exception as e:
        click.echo(f"Error generating code: {e}", err=True)
        exit(1)

@generate.command("playwright-codegen")
@click.argument("script_file", type=click.Path(exists=True, dir_okay=False, readable=True))
@click.option("--output-pom", type=click.Path(dir_okay=False, writable=True), default="generated_pom.py", help="Output file path for the generated Page Object Model.")
@click.option("--output-test", type=click.Path(dir_okay=False, writable=True), default="generated_playwright_test.py", help="Output file path for the generated Playwright test.")
@click.option("--output-data", type=click.Path(dir_okay=False, writable=True), default="generated_test_data.yaml", help="Output file path for the generated test data YAML.")
def playwright_codegen(script_file, output_pom, output_test, output_data):
    """Generate POM and test code from a Playwright Codegen Python script.

    SCRIPT_FILE: Path to the Playwright Codegen Python script.
    """
    from .codegen.playwright_parser import PlaywrightParser
    from .codegen.pom_generator import POMGenerator

    click.echo(f"Generating POM and test code from {script_file}...")
    try:
        with open(script_file, "r", encoding="utf-8") as f:
            script_content = f.read()

        parser = PlaywrightParser()
        parsed_data = parser.parse(script_content)

        generator = POMGenerator()
        generated_assets = generator.generate(parsed_data)

        # Write POM code
        pom_output_path = Path(output_pom)
        if not pom_output_path.parent.exists():
            pom_output_path.parent.mkdir(parents=True)
        with open(pom_output_path, "w", encoding="utf-8") as f:
            f.write(generated_assets["pom_code"])
        click.echo(f"Successfully generated Page Object Model to {pom_output_path}")

        # Write Test code
        test_output_path = Path(output_test)
        if not test_output_path.parent.exists():
            test_output_path.parent.mkdir(parents=True)
        with open(test_output_path, "w", encoding="utf-8") as f:
            f.write(generated_assets["test_code"])
        click.echo(f"Successfully generated Playwright test to {test_output_path}")

        # Write Data YAML
        data_output_path = Path(output_data)
        if not data_output_path.parent.exists():
            data_output_path.parent.mkdir(parents=True)
        with open(data_output_path, "w", encoding="utf-8") as f:
            yaml.dump(generated_assets["data_yaml"], f, allow_unicode=True)
        click.echo(f"Successfully generated test data to {data_output_path}")

    except Exception as e:
        click.echo(f"Error generating code: {e}", err=True)
        exit(1)


if __name__ == "__main__":
    main()