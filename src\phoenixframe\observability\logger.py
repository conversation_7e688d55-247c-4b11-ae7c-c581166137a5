"""结构化日志模块"""
import logging
import json
import uuid
from typing import Dict, Any, Optional
from datetime import datetime


class StructuredFormatter(logging.Formatter):
    """结构化JSON日志格式化器"""
    
    def __init__(self, test_run_id: Optional[str] = None):
        super().__init__()
        self.test_run_id = test_run_id or str(uuid.uuid4())
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "test_run_id": self.test_run_id,
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


class PhoenixLogger:
    """PhoenixFrame日志器"""
    
    def __init__(self, name: str, test_run_id: Optional[str] = None):
        self.logger = logging.getLogger(name)
        self.test_run_id = test_run_id or str(uuid.uuid4())
        
    def _log_with_extra(self, level: int, message: str, **kwargs) -> None:
        """带额外字段的日志记录"""
        extra = {"extra_fields": kwargs} if kwargs else {}
        self.logger.log(level, message, extra=extra)
    
    def debug(self, message: str, **kwargs) -> None:
        """调试日志"""
        self._log_with_extra(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """信息日志"""
        self._log_with_extra(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """警告日志"""
        self._log_with_extra(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """错误日志"""
        self._log_with_extra(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """严重错误日志"""
        self._log_with_extra(logging.CRITICAL, message, **kwargs)
    
    def step(self, step_name: str, **kwargs) -> None:
        """测试步骤日志"""
        self.info(f"Step: {step_name}", step_type="test_step", **kwargs)
    
    def api_request(self, method: str, url: str, **kwargs) -> None:
        """API请求日志"""
        self.info(f"API Request: {method} {url}", 
                 request_type="api", method=method, url=url, **kwargs)
    
    def api_response(self, status_code: int, response_time: float, **kwargs) -> None:
        """API响应日志"""
        self.info(f"API Response: {status_code} ({response_time:.3f}s)",
                 response_type="api", status_code=status_code, 
                 response_time=response_time, **kwargs)


# 全局日志器实例
_loggers: Dict[str, PhoenixLogger] = {}
_test_run_id: Optional[str] = None


def setup_logging(level: str = "INFO", test_run_id: Optional[str] = None, 
                 enable_console: bool = True, log_file: Optional[str] = None) -> None:
    """
    设置日志配置
    
    Args:
        level: 日志级别
        test_run_id: 测试运行ID
        enable_console: 是否启用控制台输出
        log_file: 日志文件路径
    """
    global _test_run_id
    _test_run_id = test_run_id or str(uuid.uuid4())
    
    # 设置根日志器级别
    logging.getLogger().setLevel(getattr(logging, level.upper()))
    
    # 清除现有处理器
    for handler in logging.getLogger().handlers[:]:
        logging.getLogger().removeHandler(handler)
    
    formatter = StructuredFormatter(_test_run_id)
    
    # 控制台处理器
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logging.getLogger().addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logging.getLogger().addHandler(file_handler)


def get_logger(name: str) -> PhoenixLogger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称
        
    Returns:
        PhoenixLogger: 日志器实例
    """
    if name not in _loggers:
        _loggers[name] = PhoenixLogger(name, _test_run_id)
    return _loggers[name]


def get_test_run_id() -> Optional[str]:
    """获取当前测试运行ID"""
    return _test_run_id
