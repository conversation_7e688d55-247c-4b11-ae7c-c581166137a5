"""Selenium WebDriver封装"""
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from .base_page import BasePage


class SeleniumDriver:
    """Selenium WebDriver管理器"""
    
    def __init__(self, browser: str = "chrome", headless: bool = False, options: Optional[Dict[str, Any]] = None):
        """
        初始化Selenium WebDriver
        
        Args:
            browser: 浏览器类型 ("chrome", "firefox")
            headless: 是否无头模式
            options: 额外的浏览器选项
        """
        self.browser = browser.lower()
        self.headless = headless
        self.options = options or {}
        self.driver: Optional[webdriver.Remote] = None
        
    def start(self) -> webdriver.Remote:
        """启动WebDriver"""
        if self.driver:
            return self.driver
            
        if self.browser == "chrome":
            chrome_options = ChromeOptions()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            # 添加自定义选项
            for key, value in self.options.items():
                if isinstance(value, bool) and value:
                    chrome_options.add_argument(f"--{key}")
                elif not isinstance(value, bool):
                    chrome_options.add_argument(f"--{key}={value}")
                    
            self.driver = webdriver.Chrome(options=chrome_options)
            
        elif self.browser == "firefox":
            firefox_options = FirefoxOptions()
            if self.headless:
                firefox_options.add_argument("--headless")
                
            # 添加自定义选项
            for key, value in self.options.items():
                if isinstance(value, bool) and value:
                    firefox_options.add_argument(f"--{key}")
                elif not isinstance(value, bool):
                    firefox_options.add_argument(f"--{key}={value}")
                    
            self.driver = webdriver.Firefox(options=firefox_options)
            
        else:
            raise ValueError(f"Unsupported browser: {self.browser}")
            
        return self.driver
    
    def quit(self) -> None:
        """关闭WebDriver"""
        if self.driver:
            self.driver.quit()
            self.driver = None
    
    def get_driver(self) -> Optional[webdriver.Remote]:
        """获取WebDriver实例"""
        return self.driver


class SeleniumPage(BasePage):
    """基于Selenium的页面对象"""
    
    def __init__(self, driver: webdriver.Remote):
        """
        初始化Selenium页面对象
        
        Args:
            driver: Selenium WebDriver实例
        """
        super().__init__(driver)
        self.wait = WebDriverWait(driver, 10)
    
    def navigate(self, url: Optional[str] = None) -> None:
        """导航到页面"""
        target_url = url or self.url
        if not target_url:
            raise ValueError("URL is required")
        self.driver.get(target_url)
    
    def wait_for_page_load(self, timeout: int = 10) -> bool:
        """等待页面加载完成"""
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            return True
        except TimeoutException:
            return False
    
    def is_element_present(self, locator: str) -> bool:
        """检查元素是否存在"""
        try:
            self.driver.find_element(By.CSS_SELECTOR, locator)
            return True
        except NoSuchElementException:
            return False
    
    def click_element(self, locator: str) -> None:
        """点击元素"""
        element = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, locator)))
        element.click()
    
    def input_text(self, locator: str, text: str) -> None:
        """在元素中输入文本"""
        element = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, locator)))
        element.clear()
        element.send_keys(text)
    
    def get_text(self, locator: str) -> str:
        """获取元素文本"""
        element = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, locator)))
        return element.text
    
    def wait_for_element(self, locator: str, timeout: int = 10) -> Any:
        """等待元素出现"""
        return WebDriverWait(self.driver, timeout).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, locator))
        )
