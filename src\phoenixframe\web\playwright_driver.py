"""Playwright Driver封装"""
from typing import Optional, Dict, Any
from .base_page import BasePage

try:
    from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    Browser = Any
    Page = Any
    BrowserContext = Any


class PlaywrightDriver:
    """Playwright Driver管理器"""
    
    def __init__(self, browser: str = "chromium", headless: bool = False, options: Optional[Dict[str, Any]] = None):
        """
        初始化Playwright Driver
        
        Args:
            browser: 浏览器类型 ("chromium", "firefox", "webkit")
            headless: 是否无头模式
            options: 额外的浏览器选项
        """
        if not PLAYWRIGHT_AVAILABLE:
            raise ImportError("Playwright is not installed. Please install it with: pip install playwright")
            
        self.browser_name = browser.lower()
        self.headless = headless
        self.options = options or {}
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
    def start(self) -> Page:
        """启动Playwright"""
        if self.page:
            return self.page
            
        self.playwright = sync_playwright().start()
        
        # 选择浏览器
        if self.browser_name == "chromium":
            self.browser = self.playwright.chromium.launch(headless=self.headless, **self.options)
        elif self.browser_name == "firefox":
            self.browser = self.playwright.firefox.launch(headless=self.headless, **self.options)
        elif self.browser_name == "webkit":
            self.browser = self.playwright.webkit.launch(headless=self.headless, **self.options)
        else:
            raise ValueError(f"Unsupported browser: {self.browser_name}")
            
        # 创建上下文和页面
        self.context = self.browser.new_context()
        self.page = self.context.new_page()
        
        return self.page
    
    def quit(self) -> None:
        """关闭Playwright"""
        if self.page:
            self.page.close()
            self.page = None
        if self.context:
            self.context.close()
            self.context = None
        if self.browser:
            self.browser.close()
            self.browser = None
        if self.playwright:
            self.playwright.stop()
            self.playwright = None
    
    def get_page(self) -> Optional[Page]:
        """获取Page实例"""
        return self.page


class PlaywrightPage(BasePage):
    """基于Playwright的页面对象"""
    
    def __init__(self, page: Page):
        """
        初始化Playwright页面对象
        
        Args:
            page: Playwright Page实例
        """
        super().__init__(page)
    
    def navigate(self, url: Optional[str] = None) -> None:
        """导航到页面"""
        target_url = url or self.url
        if not target_url:
            raise ValueError("URL is required")
        self.driver.goto(target_url)
    
    def wait_for_page_load(self, timeout: int = 10) -> bool:
        """等待页面加载完成"""
        try:
            self.driver.wait_for_load_state("networkidle", timeout=timeout * 1000)
            return True
        except Exception:
            return False
    
    def is_element_present(self, locator: str) -> bool:
        """检查元素是否存在"""
        try:
            element = self.driver.locator(locator)
            return element.count() > 0
        except Exception:
            return False
    
    def click_element(self, locator: str) -> None:
        """点击元素"""
        self.driver.locator(locator).click()
    
    def input_text(self, locator: str, text: str) -> None:
        """在元素中输入文本"""
        element = self.driver.locator(locator)
        element.clear()
        element.fill(text)
    
    def get_text(self, locator: str) -> str:
        """获取元素文本"""
        return self.driver.locator(locator).text_content() or ""
    
    def wait_for_element(self, locator: str, timeout: int = 10) -> Any:
        """等待元素出现"""
        return self.driver.wait_for_selector(locator, timeout=timeout * 1000)
    
    def screenshot(self, path: Optional[str] = None) -> bytes:
        """截图"""
        if path:
            self.driver.screenshot(path=path)
            with open(path, 'rb') as f:
                return f.read()
        else:
            return self.driver.screenshot()
