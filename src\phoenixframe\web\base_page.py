"""基础页面对象模型类"""
from abc import ABC, abstractmethod
from typing import Any, Optional


class BasePage(ABC):
    """页面对象模型基类，定义通用的页面操作接口"""
    
    def __init__(self, driver: Any):
        """
        初始化页面对象
        
        Args:
            driver: WebDriver实例（Selenium或Playwright）
        """
        self.driver = driver
        self.url: Optional[str] = None
    
    @abstractmethod
    def navigate(self, url: Optional[str] = None) -> None:
        """
        导航到页面
        
        Args:
            url: 页面URL，如果为None则使用self.url
        """
        pass
    
    @abstractmethod
    def wait_for_page_load(self, timeout: int = 10) -> bool:
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 页面是否加载成功
        """
        pass
    
    @abstractmethod
    def is_element_present(self, locator: str) -> bool:
        """
        检查元素是否存在
        
        Args:
            locator: 元素定位器
            
        Returns:
            bool: 元素是否存在
        """
        pass
    
    @abstractmethod
    def click_element(self, locator: str) -> None:
        """
        点击元素
        
        Args:
            locator: 元素定位器
        """
        pass
    
    @abstractmethod
    def input_text(self, locator: str, text: str) -> None:
        """
        在元素中输入文本
        
        Args:
            locator: 元素定位器
            text: 要输入的文本
        """
        pass
    
    @abstractmethod
    def get_text(self, locator: str) -> str:
        """
        获取元素文本
        
        Args:
            locator: 元素定位器
            
        Returns:
            str: 元素文本
        """
        pass
    
    def get_title(self) -> str:
        """
        获取页面标题
        
        Returns:
            str: 页面标题
        """
        return self.driver.title if hasattr(self.driver, 'title') else ""
    
    def get_current_url(self) -> str:
        """
        获取当前页面URL
        
        Returns:
            str: 当前页面URL
        """
        return self.driver.current_url if hasattr(self.driver, 'current_url') else ""
