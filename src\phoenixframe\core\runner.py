import pytest
from .config import get_config
from .hooks import trigger_hook
from typing import Callable, List, Optional

class PhoenixRunner:
    def run_tests(self, test_functions: Optional[List[Callable]] = None, test_paths: Optional[List[str]] = None, pytest_extra_args=None):
        """
        支持两种模式：
        1. 直接传入 test_functions（Python函数列表），依次执行并统计通过数量。
        2. 传入 test_paths，走pytest主流程。

        对于test_functions模式，返回字典格式 {'passed_count': int, 'failed_count': int, 'errors': []}
        对于test_paths模式，返回字典格式 {'pytest_exit_code': int, 'passed_count': int, 'failed_count': int}
        """
        config = get_config()

        if test_functions:
            # 函数模式：直接执行函数并统计结果
            result = {"passed_count": 0, "failed_count": 0, "errors": []}
            for fn in test_functions:
                try:
                    fn()
                    result["passed_count"] += 1
                except Exception as e:
                    result["failed_count"] += 1
                    result["errors"].append(str(e))
            return result

        # pytest模式：使用pytest运行测试
        pytest_args = list(test_paths) if test_paths else []
        pytest_extra_args = list(pytest_extra_args) if pytest_extra_args else []

        # 添加Allure报告配置
        if config.reporting.allure.get("enabled", True):
            results_dir = config.reporting.allure.get("results_dir", "allure-results")
            pytest_args.extend(["--alluredir", results_dir])

        pytest_args.extend(pytest_extra_args)

        # 触发钩子
        trigger_hook("on_test_run_start", config=config)
        exit_code = pytest.main(pytest_args)
        trigger_hook("on_test_run_end", exit_code=exit_code)

        # 返回结果
        return {
            "pytest_exit_code": exit_code,
            "passed_count": 0,  # TODO: 从pytest结果中提取实际数量
            "failed_count": 0   # TODO: 从pytest结果中提取实际数量
        }
