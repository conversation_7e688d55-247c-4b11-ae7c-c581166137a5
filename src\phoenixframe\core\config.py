from pathlib import Path
import yaml
from pydantic import BaseModel, Field, ValidationError, ConfigDict
import os
from typing import Dict, Any, Optional

class EnvironmentConfig(BaseModel):
    model_config = ConfigDict(extra="allow")  # Allow additional environment-specific configurations
    
    base_url: str = Field(default="http://localhost:8000")
    description: Optional[str] = None

class ReportingConfig(BaseModel):
    allure: Dict[str, Any] = Field(default_factory=lambda: {
        "enabled": True,
        "report_dir": "allure-report",
        "results_dir": "allure-results"
    })

class ConfigModel(BaseModel):
    model_config = ConfigDict(extra="allow")
    
    app_name: str = "PhoenixFrame"
    version: str = "1.0"
    environments: Dict[str, EnvironmentConfig] = Field(default_factory=lambda: {
        "default": EnvironmentConfig()
    })
    reporting: ReportingConfig = Field(default_factory=ReportingConfig)

def load_config(config_path: Path) -> ConfigModel:
    """Loads the configuration from a YAML file and environment variables."""
    if not config_path.is_file():
        raise FileNotFoundError(f"Configuration file not found at: {config_path}")

    with open(config_path, "r", encoding="utf-8") as f:
        config_data = yaml.safe_load(f)

    # Apply environment variable overrides
    _override_from_env(config_data)

    try:
        return ConfigModel(** config_data)
    except ValidationError as e:
        raise ValueError(f"Configuration validation failed: {e}") from e


def _override_from_env(config_data: Dict[str, Any]) -> None:
    """Override configuration values with environment variables.

    Environment variable format: PHOENIX_<SECTION>_<SUBSECTION>_<KEY>
    Examples:
    - PHOENIX_ENVIRONMENTS_TEST_BASE_URL -> config_data["environments"]["test"]["base_url"]
    - PHOENIX_REPORTING_ALLURE_ENABLED -> config_data["reporting"]["allure"]["enabled"]

    Special handling for compound keys like BASE_URL -> base_url
    """
    prefix = "PHOENIX_"
    for key, value in os.environ.items():
        if key.startswith(prefix):
            # Remove prefix and convert to lowercase
            env_key = key[len(prefix):].lower()

            # Handle specific known patterns
            if env_key.startswith("environments_"):
                # PHOENIX_ENVIRONMENTS_TEST_BASE_URL -> ["environments", "test", "base_url"]
                parts = env_key.split("_", 2)  # Split into max 3 parts
                if len(parts) >= 3:
                    section, env_name, field_key = parts
                    # Convert compound field names back to snake_case
                    if field_key == "baseurl":
                        field_key = "base_url"

                    if section not in config_data:
                        config_data[section] = {}
                    if env_name not in config_data[section]:
                        config_data[section][env_name] = {}

                    # Convert value types
                    if value.lower() in ('true', 'false'):
                        config_data[section][env_name][field_key] = value.lower() == 'true'
                    elif value.isdigit():
                        config_data[section][env_name][field_key] = int(value)
                    else:
                        config_data[section][env_name][field_key] = value

            elif env_key.startswith("reporting_"):
                # PHOENIX_REPORTING_ALLURE_ENABLED -> ["reporting", "allure", "enabled"]
                parts = env_key.split("_", 2)  # Split into max 3 parts
                if len(parts) >= 3:
                    section, subsection, field_key = parts

                    if section not in config_data:
                        config_data[section] = {}
                    if subsection not in config_data[section]:
                        config_data[section][subsection] = {}

                    # Convert value types
                    if value.lower() in ('true', 'false'):
                        config_data[section][subsection][field_key] = value.lower() == 'true'
                    elif value.isdigit():
                        config_data[section][subsection][field_key] = int(value)
                    else:
                        config_data[section][subsection][field_key] = value
            else:
                # Generic handling for simple keys
                path_parts = env_key.split("_")
                current = config_data

                # Navigate to the nested structure, creating dicts as needed
                for part in path_parts[:-1]:
                    if part not in current:
                        current[part] = {}
                    elif not isinstance(current[part], dict):
                        # If the current value is not a dict, we can't navigate further
                        break
                    current = current[part]
                else:
                    # Set the final value only if we successfully navigated the path
                    final_key = path_parts[-1]
                    if value.lower() in ('true', 'false'):
                        current[final_key] = value.lower() == 'true'
                    elif value.isdigit():
                        current[final_key] = int(value)
                    else:
                        current[final_key] = value


# Global configuration instance
_global_config: Optional[ConfigModel] = None


def get_config() -> ConfigModel:
    """Returns the global configuration instance."""
    global _global_config
    if _global_config is None:
        # Try to load from default location
        default_config_path = Path("configs/phoenix.yaml")
        if default_config_path.exists():
            _global_config = load_config(default_config_path)
        else:
            # Use default configuration
            _global_config = ConfigModel()
    return _global_config


def set_config(config: ConfigModel) -> None:
    """Sets the global configuration instance."""
    global _global_config
    _global_config = config
