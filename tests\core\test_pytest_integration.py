import pytest
from phoenixframe.core.runner import <PERSON><PERSON><PERSON>ner

@pytest.fixture(scope="function")
def simple_test_file(tmp_path):
    test_dir = tmp_path / "simple_tests"
    test_dir.mkdir()
    test_file = test_dir / "test_simple.py"
    test_file.write_text("""
def test_always_passes():
    assert True
""")
    return str(test_file)

def test_pytest_can_discover_and_run_simple_test(simple_test_file):
    """
    Tests if pytest can discover and run a simple test through the PhoenixRunner.
    """
    runner = PhoenixRunner()
    result = runner.run_tests(test_paths=[simple_test_file])
    assert result["pytest_exit_code"] == 0  # pytest.main returns 0 for success
    assert isinstance(result, dict)
    assert "pytest_exit_code" in result
