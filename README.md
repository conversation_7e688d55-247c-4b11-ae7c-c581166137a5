# PhoenixFrameAn enterprise-grade test automation solution.## OverviewPhoenixFrame is a Python-based, comprehensive test automation solution designed for enterprise applications. It addresses the challenges of diverse test types, complex technology stacks, inefficient collaboration, and difficulties in quality and issue traceability.**Key Features (v3.2 Highlights):**-   **Unified Testing**: A single framework for Web UI, API, Performance, and Security testing.-   **Enhanced Observability**: Integrated Logging, Tracing, and Metrics for robust debugging.-   **Declarative & Programmatic APIs**: Flexible API testing with both YAML-based and Pythonic approaches.-   **Test Asset Codification Engine (NEW!)**: Revolutionize your test creation process by automatically generating high-quality, maintainable test code from:-   **Playwright Codegen scripts**: Convert raw recorded scripts into Page Object Model (POM) based, data-driven tests.-   **HAR (HTTP Archive) files**: Generate API test cases directly from network traffic recordings.-   **OpenAPI/Swagger specifications**: Scaffold comprehensive API test suites from your API definitions.-   **CLI Tools**: Powerful command-line interface for project management, code generation, and environment diagnostics.## InstallationPhoenixFrame uses `uv` for dependency management. Ensure `uv` is installed on your system.```bash# Install uv (if not already installed)# pip install uv# Clone the repositorygit clone https://github.com/your-repo/PhoenixFrame.gitcd PhoenixFrame# Create and activate a virtual environmentuv venvsource .venv/bin/activate # On Windows: .venv\Scripts\activate# Install dependenciesuv pip sync pyproject.toml```## Usage### CLI Commands-   **`phoenix doctor`**: Diagnose your environment.```bashphoenix doctor```-   **`phoenix env list`**: List configured environments.```bashphoenix env list```-   **`phoenix generate`**: Generate test code from various assets.-   **From HAR file:**```bashphoenix generate har your_traffic.har --output generated_api_tests.py```-   **From OpenAPI/Swagger spec:**```bashphoenix generate openapi your_api_spec.yaml --output generated_api_skeletons.py```-   **From Playwright Codegen script:**```bashphoenix generate playwright-codegen your_recorded_script.py \--output-pom my_pom.py \--output-test my_ui_test.py \--output-data my_test_data.yaml```## ContributingRefer to `PhoenixFrame-开发规则书.md` for development guidelines and contribution process.## LicenseThis project is licensed under the MIT License - see the LICENSE file for details.```